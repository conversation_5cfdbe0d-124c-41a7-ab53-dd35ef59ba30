<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>金融交易管理系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 250px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .logo {
            text-align: center;
            padding: 0 20px 30px;
            border-bottom: 1px solid #e0e0e0;
            margin-bottom: 30px;
        }

        .logo h1 {
            color: #667eea;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            color: #666;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .nav-link:hover,
        .nav-link.active {
            background: linear-gradient(90deg, rgba(102, 126, 234, 0.1) 0%, transparent 100%);
            color: #667eea;
            border-left-color: #667eea;
        }

        .nav-link i {
            margin-right: 12px;
            width: 20px;
            text-align: center;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 30px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h2 {
            color: #333;
            font-size: 1.8rem;
            font-weight: 600;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        /* 页面内容 */
        .page-content {
            display: none;
        }

        .page-content.active {
            display: block;
        }

        /* 仪表板样式 */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .stat-card h3 {
            color: #666;
            font-size: 0.9rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-card .icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .stat-card .value {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }

        .stat-card .change {
            font-size: 0.85rem;
            color: #28a745;
        }

        /* 快速操作按钮 */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            text-decoration: none;
            color: #333;
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .action-btn i {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #667eea;
        }

        .action-btn span {
            display: block;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 侧边栏 -->
        <nav class="sidebar">
            <div class="logo">
                <h1><i class="fas fa-chart-line"></i> 交易系统</h1>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#" class="nav-link active" data-page="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>仪表板</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="orders">
                        <i class="fas fa-shopping-cart"></i>
                        <span>订单管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="transactions">
                        <i class="fas fa-exchange-alt"></i>
                        <span>交易流程</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="approvals">
                        <i class="fas fa-check-circle"></i>
                        <span>审批中心</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="analytics">
                        <i class="fas fa-chart-bar"></i>
                        <span>数据统计</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-page="settings">
                        <i class="fas fa-cog"></i>
                        <span>系统设置</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 头部 -->
            <header class="header">
                <h2 id="page-title">仪表板</h2>
                <div class="user-info">
                    <span>欢迎，管理员</span>
                    <div class="user-avatar">A</div>
                </div>
            </header>

            <!-- 仪表板页面 -->
            <div id="dashboard" class="page-content active">
                <div class="dashboard-grid">
                    <div class="stat-card">
                        <div class="stat-card-header">
                            <h3>今日订单</h3>
                            <div class="icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                        </div>
                        <div class="value">24</div>
                        <div class="change">+12% 较昨日</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-header">
                            <h3>交易金额</h3>
                            <div class="icon" style="background: linear-gradient(135deg, #f093fb, #f5576c);">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                        </div>
                        <div class="value">¥128.5万</div>
                        <div class="change">+8.5% 较昨日</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-header">
                            <h3>待审批</h3>
                            <div class="icon" style="background: linear-gradient(135deg, #4facfe, #00f2fe);">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>
                        <div class="value">8</div>
                        <div class="change">需要处理</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-header">
                            <h3>成功率</h3>
                            <div class="icon" style="background: linear-gradient(135deg, #43e97b, #38f9d7);">
                                <i class="fas fa-chart-line"></i>
                            </div>
                        </div>
                        <div class="value">94.2%</div>
                        <div class="change">+2.1% 较上月</div>
                    </div>
                </div>

                <div class="quick-actions">
                    <button class="action-btn" onclick="showPage('orders', '新建渠道订单')">
                        <i class="fas fa-plus-circle"></i>
                        <span>新建渠道订单</span>
                    </button>
                    <button class="action-btn" onclick="showPage('orders', '新建市场订单')">
                        <i class="fas fa-chart-area"></i>
                        <span>新建市场订单</span>
                    </button>
                    <button class="action-btn" onclick="showPage('approvals', '付款申请')">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>付款申请</span>
                    </button>
                    <button class="action-btn" onclick="showPage('approvals', '收款申请')">
                        <i class="fas fa-hand-holding-usd"></i>
                        <span>收款申请</span>
                    </button>
                </div>
            </div>

            <!-- 订单管理页面 -->
            <div id="orders" class="page-content">
                <div class="dashboard-grid">
                    <div class="stat-card">
                        <h3>渠道订单</h3>
                        <div class="quick-actions" style="margin: 20px 0;">
                            <button class="action-btn" onclick="createOrder('channel', 'internal')">
                                <i class="fas fa-building"></i>
                                <span>内部交易</span>
                            </button>
                            <button class="action-btn" onclick="createOrder('channel', 'external')">
                                <i class="fas fa-external-link-alt"></i>
                                <span>外部交易</span>
                            </button>
                        </div>
                    </div>

                    <div class="stat-card">
                        <h3>市场订单</h3>
                        <div class="quick-actions" style="margin: 20px 0;">
                            <button class="action-btn" onclick="createOrder('market', 'bid')">
                                <i class="fas fa-gavel"></i>
                                <span>竞价成功订单</span>
                            </button>
                            <button class="action-btn" onclick="createOrder('market', 'external')">
                                <i class="fas fa-handshake"></i>
                                <span>外部成交订单</span>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <h3>最近订单</h3>
                    <div style="overflow-x: auto; margin-top: 20px;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="background: #f8f9fa;">
                                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">订单号</th>
                                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">类型</th>
                                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">金额</th>
                                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">状态</th>
                                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">创建时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td style="padding: 12px; border-bottom: 1px solid #dee2e6;">#ORD001</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #dee2e6;">渠道-内部</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #dee2e6;">¥50,000</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #dee2e6;"><span style="background: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.8rem;">已完成</span></td>
                                    <td style="padding: 12px; border-bottom: 1px solid #dee2e6;">2024-01-15 10:30</td>
                                </tr>
                                <tr>
                                    <td style="padding: 12px; border-bottom: 1px solid #dee2e6;">#ORD002</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #dee2e6;">市场-竞价</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #dee2e6;">¥75,000</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #dee2e6;"><span style="background: #ffc107; color: #212529; padding: 4px 8px; border-radius: 4px; font-size: 0.8rem;">进行中</span></td>
                                    <td style="padding: 12px; border-bottom: 1px solid #dee2e6;">2024-01-15 14:20</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 交易流程页面 -->
            <div id="transactions" class="page-content">
                <div class="stat-card">
                    <h3>交易流程管理</h3>
                    <p style="margin: 15px 0; color: #666;">管理和跟踪所有交易流程的进度</p>

                    <div class="dashboard-grid" style="margin-top: 20px;">
                        <div class="stat-card">
                            <h4>内部交易流程</h4>
                            <ul style="list-style: none; padding: 0; margin: 15px 0;">
                                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✓ 选择市场业务员</li>
                                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✓ 上传聊天截图、合同附件</li>
                                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✓ 填写最终成交价</li>
                                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✓ 设置分成比例</li>
                                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✓ 填写额外费用</li>
                            </ul>
                        </div>

                        <div class="stat-card">
                            <h4>外部交易流程</h4>
                            <ul style="list-style: none; padding: 0; margin: 15px 0;">
                                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✓ 填写外部公司信息</li>
                                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✓ 上传相关附件</li>
                                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✓ 确认成交价格</li>
                                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✓ 设置分成方案</li>
                                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✓ 处理额外费用</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 审批中心页面 -->
            <div id="approvals" class="page-content">
                <div class="dashboard-grid">
                    <div class="stat-card">
                        <h3>发起申请</h3>
                        <div class="quick-actions" style="margin: 20px 0;">
                            <button class="action-btn" onclick="createApproval('payment')">
                                <i class="fas fa-money-bill-wave"></i>
                                <span>付款申请</span>
                            </button>
                            <button class="action-btn" onclick="createApproval('receipt')">
                                <i class="fas fa-hand-holding-usd"></i>
                                <span>收款申请</span>
                            </button>
                        </div>
                    </div>

                    <div class="stat-card">
                        <h3>待处理审批</h3>
                        <div style="margin-top: 20px;">
                            <div style="padding: 15px; border-left: 4px solid #ffc107; background: #fff3cd; margin-bottom: 10px; border-radius: 4px;">
                                <strong>付款申请 #PAY001</strong><br>
                                <small>申请人: 张三 | 金额: ¥50,000 | 2小时前</small>
                            </div>
                            <div style="padding: 15px; border-left: 4px solid #17a2b8; background: #d1ecf1; margin-bottom: 10px; border-radius: 4px;">
                                <strong>收款申请 #REC001</strong><br>
                                <small>申请人: 李四 | 金额: ¥30,000 | 4小时前</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据统计页面 -->
            <div id="analytics" class="page-content">
                <div class="dashboard-grid">
                    <div class="stat-card">
                        <h3>交易统计</h3>
                        <div style="height: 200px; display: flex; align-items: center; justify-content: center; background: #f8f9fa; border-radius: 8px; margin-top: 15px;">
                            <i class="fas fa-chart-line" style="font-size: 3rem; color: #667eea;"></i>
                            <span style="margin-left: 15px; color: #666;">图表数据展示区域</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <h3>收支分析</h3>
                        <div style="margin-top: 15px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                <span>本月收入:</span>
                                <strong style="color: #28a745;">¥2,580,000</strong>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                <span>本月支出:</span>
                                <strong style="color: #dc3545;">¥1,920,000</strong>
                            </div>
                            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                <span>净利润:</span>
                                <strong style="color: #667eea;">¥660,000</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统设置页面 -->
            <div id="settings" class="page-content">
                <div class="stat-card">
                    <h3>系统设置</h3>
                    <div style="margin-top: 20px;">
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">默认分成比例</label>
                            <input type="text" value="渠道60% : 市场40%" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                        </div>
                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">审批流程设置</label>
                            <select style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                <option>单级审批</option>
                                <option>多级审批</option>
                            </select>
                        </div>
                        <button style="background: #667eea; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer;">
                            保存设置
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="background: white; margin: 5% auto; padding: 30px; border-radius: 15px; max-width: 600px; max-height: 80vh; overflow-y: auto;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 id="modal-title">标题</h3>
                <button onclick="closeModal()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer;">&times;</button>
            </div>
            <div id="modal-content">
                <!-- 动态内容 -->
            </div>
        </div>
    </div>

    <script>
        // 页面导航
        function showPage(pageId, title) {
            // 隐藏所有页面
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
            });

            // 显示目标页面
            document.getElementById(pageId).classList.add('active');

            // 更新标题
            document.getElementById('page-title').textContent = title || getPageTitle(pageId);

            // 更新导航状态
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            document.querySelector(`[data-page="${pageId}"]`).classList.add('active');
        }

        function getPageTitle(pageId) {
            const titles = {
                'dashboard': '仪表板',
                'orders': '订单管理',
                'transactions': '交易流程',
                'approvals': '审批中心',
                'analytics': '数据统计',
                'settings': '系统设置'
            };
            return titles[pageId] || '系统';
        }

        // 导航点击事件
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const pageId = this.getAttribute('data-page');
                showPage(pageId);
            });
        });

        // 创建订单
        function createOrder(type, subType) {
            const titles = {
                'channel-internal': '创建渠道内部交易订单',
                'channel-external': '创建渠道外部交易订单',
                'market-bid': '创建市场竞价成功订单',
                'market-external': '创建市场外部成交订单'
            };

            const title = titles[`${type}-${subType}`];
            showModal(title, getOrderForm(type, subType));
        }

        // 创建审批
        function createApproval(type) {
            const titles = {
                'payment': '创建付款申请',
                'receipt': '创建收款申请'
            };

            showModal(titles[type], getApprovalForm(type));
        }

        // 显示模态框
        function showModal(title, content) {
            document.getElementById('modal-title').textContent = title;
            document.getElementById('modal-content').innerHTML = content;
            document.getElementById('modal').style.display = 'block';
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('modal').style.display = 'none';
        }

        // 获取订单表单
        function getOrderForm(type, subType) {
            let fields = [];

            if (type === 'channel') {
                if (subType === 'internal') {
                    fields = [
                        '市场业务员',
                        '聊天截图',
                        '合同附件',
                        '最终成交价',
                        '分成比例设置',
                        '额外费用',
                        '备注'
                    ];
                } else {
                    fields = [
                        '外部公司信息',
                        '聊天截图',
                        '合同附件',
                        '最终成交价',
                        '分成比例设置',
                        '外部分成比例',
                        '额外费用',
                        '备注'
                    ];
                }
            } else {
                if (subType === 'bid') {
                    fields = [
                        '聊天截图',
                        '合同附件',
                        '最终成交价',
                        '分成比例设置',
                        '额外费用',
                        '备注'
                    ];
                } else {
                    fields = [
                        '外部公司信息',
                        '聊天截图',
                        '合同附件',
                        '最终成交价',
                        '外部账号信息',
                        '分成比例',
                        '额外费用',
                        '备注'
                    ];
                }
            }

            return generateForm(fields);
        }

        // 获取审批表单
        function getApprovalForm(type) {
            let fields = [];

            if (type === 'payment') {
                fields = [
                    '发起人',
                    '创建人部门',
                    '市场部业务员',
                    '渠道部业务员',
                    '付款类型',
                    '付款事由',
                    '付款金额',
                    '交易账号UID',
                    '交易公司对象',
                    '合同编号',
                    '已收金额',
                    '是否换绑',
                    '付款方式',
                    '收款人',
                    '银行账户',
                    '开户银行',
                    '备注'
                ];
            } else {
                fields = [
                    '发起人',
                    '创建人部门',
                    '收款业务员',
                    '收款客户',
                    '收款类型',
                    '收款金额',
                    '交易账号UID',
                    '收款归属',
                    '合同编号',
                    '备注'
                ];
            }

            return generateForm(fields);
        }

        // 生成表单HTML
        function generateForm(fields) {
            let html = '<form style="max-height: 400px; overflow-y: auto;">';

            fields.forEach(field => {
                html += `
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">${field}:</label>
                        <input type="text" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;" placeholder="请输入${field}">
                    </div>
                `;
            });

            html += `
                <div style="text-align: right; margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee;">
                    <button type="button" onclick="closeModal()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin-right: 10px; cursor: pointer;">取消</button>
                    <button type="submit" style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">提交</button>
                </div>
            </form>`;

            return html;
        }

        // 点击模态框外部关闭
        document.getElementById('modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>

</body>
</html>