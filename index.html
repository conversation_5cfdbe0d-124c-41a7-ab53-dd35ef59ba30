<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交互原型网页</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            display: flex;
            justify-content: center;
            width: 100%;
            max-width: 1200px;
            margin-bottom: 40px;
        }
        .channel-market-section {
            display: flex;
            justify-content: space-around;
            width: 100%;
            margin-bottom: 40px;
        }
        .box {
            border: 1px solid #ccc;
            padding: 15px;
            margin: 10px;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-radius: 8px;
            text-align: center;
            min-width: 150px;
        }
        .process-box {
            border: 1px solid #ccc;
            padding: 15px;
            margin: 10px;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-radius: 8px;
            text-align: left;
            min-width: 300px;
            max-width: 400px;
        }
        .diamond {
            width: 100px;
            height: 100px;
            background-color: #fff;
            border: 1px solid #ccc;
            transform: rotate(45deg);
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px auto;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-radius: 8px;
        }
        .diamond-text {
            transform: rotate(-45deg);
            font-weight: bold;
            color: #333;
        }
        .arrow {
            font-size: 30px;
            margin: 0 10px;
            color: #555;
        }
        .line {
            width: 2px;
            height: 50px;
            background-color: #ccc;
            margin: 0 auto;
        }
        .horizontal-line {
            width: 100px;
            height: 2px;
            background-color: #ccc;
            margin: 0 10px;
        }
        .flow-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 40px;
        }
        .sub-flow {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            width: 100%;
        }
        .sub-flow-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0 20px;
        }
        .sub-flow-item .horizontal-line {
            width: 150px;
            margin: 10px 0;
        }
        .sub-flow-item .arrow {
            margin: 10px 0;
        }
        .sub-flow-item .process-box {
            margin-top: 0;
        }
        .approval-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 40px;
        }
        .approval-section .line {
            height: 80px;
        }
        .payment-receipt-section {
            display: flex;
            justify-content: center;
            width: 100%;
            margin-top: 20px;
        }
        .payment-receipt-section .process-box {
            min-width: 450px;
            max-width: 500px;
            margin: 0 20px;
        }
        .process-box ul {
            list-style: none;
            padding: 0;
            margin: 0;
            text-align: left;
        }
        .process-box li {
            margin-bottom: 5px;
        }
        .hidden {
            display: none;
        }
        .modal {
            display: none; /* Hidden by default */
            position: fixed; /* Stay in place */
            z-index: 1; /* Sit on top */
            left: 0;
            top: 0;
            width: 100%; /* Full width */
            height: 100%; /* Full height */
            overflow: auto; /* Enable scroll if needed */
            background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
        }
        .modal-content {
            background-color: #fefefe;
            margin: 15% auto; /* 15% from the top and centered */
            padding: 20px;
            border: 1px solid #888;
            width: 80%; /* Could be more or less, depending on screen size */
            max-width: 500px;
            border-radius: 8px;
            position: relative;
        }
        .close-button {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }
        .close-button:hover,
        .close-button:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }
        .modal-buttons {
            text-align: right;
            margin-top: 20px;
        }
        .modal-buttons button {
            padding: 8px 15px;
            margin-left: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .modal-buttons .confirm {
            background-color: #4CAF50;
            color: white;
        }
        .modal-buttons .cancel {
            background-color: #f44336;
            color: white;
        }
    </style>
</head>
<body>

    <div class="channel-market-section">
        <div class="flow-section">
            <div class="box">渠道</div>
            <div class="line"></div>
            <div class="diamond" id="channelOrderBtn">
                <span class="diamond-text">点击出售或新增订单</span>
            </div>
            <div class="line"></div>
            <div class="sub-flow">
                <div class="sub-flow-item">
                    <div class="horizontal-line"></div>
                    <div class="arrow">↓ 内部交易</div>
                    <div class="process-box hidden" id="internalTransactionChannel">
                        <ul>
                            <li>1.选择市场业务员</li>
                            <li>2.上传聊天截图、合同附件</li>
                            <li>3.填写最终成交价</li>
                            <li>4.选择是否正常分成比例 (正常:渠道60%市场40%)</li>
                            <li>5.非正常分成</li>
                            <li>有其他人员参与分成</li>
                            <li>填写A分成比例(可以填多个,每个人员的比例可能不同)</li>
                            <li>6.填写外部分成比例</li>
                            <li>7.填写是否有额外费用(返佣,改实名技术费,备份包,面交费,快递费)</li>
                            <li>8.备注</li>
                        </ul>
                    </div>
                </div>
                <div class="sub-flow-item">
                    <div class="horizontal-line"></div>
                    <div class="arrow">↓ 外部交易</div>
                    <div class="process-box hidden" id="externalTransactionChannel">
                        <ul>
                            <li>1.填写外部公司或者号贩子信息</li>
                            <li>2.上传聊天截图、合同等附件</li>
                            <li>3.填写最终成交价</li>
                            <li>4.选择是否正常分成比例 (正常:渠道60%市场40%)</li>
                            <li>5.非正常分成</li>
                            <li>有其他人员参与分成</li>
                            <li>填写A分成比例(可以填多个,每个人员的比例可能不同)</li>
                            <li>6.填写外部分成比例</li>
                            <li>7.填写是否有额外费用(返佣,改实名技术费,备份包,面交费,快递费)</li>
                            <li>8.备注</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="flow-section">
            <div class="box">市场</div>
            <div class="line"></div>
            <div class="diamond" id="marketOrderBtn">
                <span class="diamond-text">点击竞价中或新增订单</span>
            </div>
            <div class="line"></div>
            <div class="sub-flow">
                <div class="sub-flow-item">
                    <div class="horizontal-line"></div>
                    <div class="arrow">↓ 竞价成功生成的订单</div>
                    <div class="process-box hidden" id="bidSuccessMarket">
                        <ul>
                            <li>1.上传聊天截图、合同附件</li>
                            <li>2.填写最终成交价</li>
                            <li>3.选择是否正常分成比例 (正常:渠道60%市场40%)</li>
                            <li>4.非正常分成</li>
                            <li>填写A分成比例(可以填多个,每个人员的比例可能不同)</li>
                            <li>5.填写是否有额外费用(返佣,改实名技术费,备份包,面交费,快递费)</li>
                            <li>6.备注</li>
                        </ul>
                    </div>
                </div>
                <div class="sub-flow-item">
                    <div class="horizontal-line"></div>
                    <div class="arrow">↓ 外部成交订单</div>
                    <div class="process-box hidden" id="externalDealMarket">
                        <ul>
                            <li>1.填写外部公司或者号贩子信息</li>
                            <li>2.上传聊天截图、合同等附件</li>
                            <li>3.填写最终成交价</li>
                            <li>4.填写外部账号信息(平台,粉丝数,uid,账号昵称,非必填)</li>
                            <li>5.填写分成比例</li>
                            <li>6.填写是否有额外费用(返佣,改实名技术费,备份包,面交费,快递费)</li>
                            <li>7.备注</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="approval-section">
        <div class="box">发起付款/收款审批</div>
        <div class="line"></div>
        <div class="payment-receipt-section">
            <div class="sub-flow-item">
                <div class="horizontal-line"></div>
                <div class="arrow">↓ 付款申请</div>
                <div class="process-box hidden" id="paymentApplication">
                    <ul>
                        <li>1.发起人</li>
                        <li>2.创建人部门</li>
                        <li>3.市场部业务员</li>
                        <li>4.渠道部业务员</li>
                        <li>5.付款类型(改实名,蓝改,不改实名,未实名)</li>
                        <li>6.付款事由(垫款,定金,全款,尾款,囤号)</li>
                        <li>7.付款金额(元)</li>
                        <li>8.交易账号uid</li>
                        <li>9.交易公司对象(千川,多多,外部公司等)</li>
                        <li>10.合同编号</li>
                        <li>11.已收金额</li>
                        <li>12.是否换绑</li>
                        <li>13.付款方式(银行卡,对公转账,微信,支付宝)</li>
                        <li>14.收款人</li>
                        <li>15.银行账户</li>
                        <li>16.开户银行</li>
                        <li>17.备注</li>
                    </ul>
                </div>
            </div>
            <div class="sub-flow-item">
                <div class="horizontal-line"></div>
                <div class="arrow">↓ 收款申请</div>
                <div class="process-box hidden" id="receiptApplication">
                    <ul>
                        <li>1.发起人</li>
                        <li>2.创建人部门</li>
                        <li>3.收款业务员</li>
                        <li>4.收款客户</li>
                        <li>5.收款类型(定金,全款,尾款)</li>
                        <li>6.收款金额</li>
                        <li>7.交易账号uid</li>
                        <li>8.收款归属(公司-渠道/市场)</li>
                        <li>9.合同编号</li>
                        <li>10.备注</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Channel Order -->
    <div id="channelModal" class="modal">
        <div class="modal-content">
            <span class="close-button" onclick="closeModal('channelModal')">&times;</span>
            <h2>选择交易类型</h2>
            <p>请选择渠道订单的交易类型：</p>
            <div class="modal-buttons">
                <button class="confirm" onclick="showChannelTransaction('internal')">内部交易</button>
                <button class="confirm" onclick="showChannelTransaction('external')">外部交易</button>
            </div>
        </div>
    </div>

    <!-- Modal for Market Order -->
    <div id="marketModal" class="modal">
        <div class="modal-content">
            <span class="close-button" onclick="closeModal('marketModal')">&times;</span>
            <h2>选择订单类型</h2>
            <p>请选择市场订单的类型：</p>
            <div class="modal-buttons">
                <button class="confirm" onclick="showMarketTransaction('bidSuccess')">竞价成功生成的订单</button>
                <button class="confirm" onclick="showMarketTransaction('externalDeal')">外部成交订单</button>
            </div>
        </div>
    </div>

    <!-- Modal for Approval -->
    <div id="approvalModal" class="modal">
        <div class="modal-content">
            <span class="close-button" onclick="closeModal('approvalModal')">&times;</span>
            <h2>选择审批类型</h2>
            <p>请选择发起审批的类型：</p>
            <div class="modal-buttons">
                <button class="confirm" onclick="showApprovalType('payment')">付款申请</button>
                <button class="confirm" onclick="showApprovalType('receipt')">收款申请</button>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('channelOrderBtn').addEventListener('click', function() {
            document.getElementById('channelModal').style.display = 'block';
        });

        document.getElementById('marketOrderBtn').addEventListener('click', function() {
            document.getElementById('marketModal').style.display = 'block';
        });

        document.querySelector('.approval-section .box').addEventListener('click', function() {
            document.getElementById('approvalModal').style.display = 'block';
        });

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function hideAllChannelTransactions() {
            document.getElementById('internalTransactionChannel').classList.add('hidden');
            document.getElementById('externalTransactionChannel').classList.add('hidden');
        }

        function showChannelTransaction(type) {
            hideAllChannelTransactions();
            if (type === 'internal') {
                document.getElementById('internalTransactionChannel').classList.remove('hidden');
            } else if (type === 'external') {
                document.getElementById('externalTransactionChannel').classList.remove('hidden');
            }
            closeModal('channelModal');
        }

        function hideAllMarketTransactions() {
            document.getElementById('bidSuccessMarket').classList.add('hidden');
            document.getElementById('externalDealMarket').classList.add('hidden');
        }

        function showMarketTransaction(type) {
            hideAllMarketTransactions();
            if (type === 'bidSuccess') {
                document.getElementById('bidSuccessMarket').classList.remove('hidden');
            } else if (type === 'externalDeal') {
                document.getElementById('externalDealMarket').classList.remove('hidden');
            }
            closeModal('marketModal');
        }

        function hideAllApprovalTypes() {
            document.getElementById('paymentApplication').classList.add('hidden');
            document.getElementById('receiptApplication').classList.add('hidden');
        }

        function showApprovalType(type) {
            hideAllApprovalTypes();
            if (type === 'payment') {
                document.getElementById('paymentApplication').classList.remove('hidden');
            } else if (type === 'receipt') {
                document.getElementById('receiptApplication').classList.remove('hidden');
            }
            closeModal('approvalModal');
        }
    </script>

</body>
</html>